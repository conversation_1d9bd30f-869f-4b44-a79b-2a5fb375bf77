'use client';

import { createContext, useContext, ReactNode, useState, useCallback } from 'react';

export type TransactionStatus = 'idle' | 'pending' | 'confirming' | 'success' | 'error';

export interface Transaction {
  id: string;
  type: 'stake' | 'unstake' | 'claim' | 'approve' | 'distribute';
  status: TransactionStatus;
  hash?: string;
  error?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface TransactionContextType {
  transactions: Transaction[];
  addTransaction: (transaction: Omit<Transaction, 'id' | 'timestamp'>) => string;
  updateTransaction: (id: string, updates: Partial<Transaction>) => void;
  removeTransaction: (id: string) => void;
  clearTransactions: () => void;
  getTransactionsByType: (type: Transaction['type']) => Transaction[];
  getPendingTransactions: () => Transaction[];
}

const TransactionContext = createContext<TransactionContextType | undefined>(undefined);

interface TransactionProviderProps {
  children: ReactNode;
}

export function TransactionProvider({ children }: TransactionProviderProps) {
  const [transactions, setTransactions] = useState<Transaction[]>([]);

  const addTransaction = useCallback((transaction: Omit<Transaction, 'id' | 'timestamp'>) => {
    const id = `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newTransaction: Transaction = {
      ...transaction,
      id,
      timestamp: Date.now(),
    };
    
    setTransactions(prev => [newTransaction, ...prev]);
    return id;
  }, []);

  const updateTransaction = useCallback((id: string, updates: Partial<Transaction>) => {
    setTransactions(prev => 
      prev.map(tx => 
        tx.id === id ? { ...tx, ...updates } : tx
      )
    );
  }, []);

  const removeTransaction = useCallback((id: string) => {
    setTransactions(prev => prev.filter(tx => tx.id !== id));
  }, []);

  const clearTransactions = useCallback(() => {
    setTransactions([]);
  }, []);

  const getTransactionsByType = useCallback((type: Transaction['type']) => {
    return transactions.filter(tx => tx.type === type);
  }, [transactions]);

  const getPendingTransactions = useCallback(() => {
    return transactions.filter(tx => 
      tx.status === 'pending' || tx.status === 'confirming'
    );
  }, [transactions]);

  const value: TransactionContextType = {
    transactions,
    addTransaction,
    updateTransaction,
    removeTransaction,
    clearTransactions,
    getTransactionsByType,
    getPendingTransactions,
  };

  return (
    <TransactionContext.Provider value={value}>
      {children}
    </TransactionContext.Provider>
  );
}

export function useTransactions() {
  const context = useContext(TransactionContext);
  if (context === undefined) {
    throw new Error('useTransactions must be used within a TransactionProvider');
  }
  return context;
}

// Hook for managing individual transaction lifecycle
export function useTransactionManager() {
  const { addTransaction, updateTransaction } = useTransactions();

  const createTransaction = useCallback((
    type: Transaction['type'],
    metadata?: Record<string, any>
  ) => {
    const id = addTransaction({
      type,
      status: 'pending',
      metadata,
    });

    const updateStatus = (status: TransactionStatus, hash?: string, error?: string) => {
      updateTransaction(id, { status, hash, error });
    };

    const setHash = (hash: string) => {
      updateTransaction(id, { hash, status: 'confirming' });
    };

    const setSuccess = () => {
      updateTransaction(id, { status: 'success' });
    };

    const setError = (error: string) => {
      updateTransaction(id, { status: 'error', error });
    };

    return {
      id,
      updateStatus,
      setHash,
      setSuccess,
      setError,
    };
  }, [addTransaction, updateTransaction]);

  return { createTransaction };
}
