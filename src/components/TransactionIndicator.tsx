'use client';

import { useState } from 'react';
import { useTransactions } from '@/contexts/TransactionContext';
import {
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import TransactionManager from './TransactionManager';

const TransactionIndicator = () => {
  const { transactions, getPendingTransactions } = useTransactions();
  const [isExpanded, setIsExpanded] = useState(false);
  const [showManager, setShowManager] = useState(false);

  const pendingTransactions = getPendingTransactions();
  const recentTransactions = transactions
    .filter(tx => tx.status === 'success' || tx.status === 'error')
    .slice(0, 3);

  // Don't show if no transactions
  if (transactions.length === 0) return null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
      case 'confirming':
        return <ClockIcon className="w-4 h-4 text-yellow-400 animate-spin" />;
      case 'success':
        return <CheckCircleIcon className="w-4 h-4 text-green-400" />;
      case 'error':
        return <ExclamationCircleIcon className="w-4 h-4 text-red-400" />;
      default:
        return <ClockIcon className="w-4 h-4 text-gray-400" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'stake': return 'Staking';
      case 'unstake': return 'Unstaking';
      case 'claim': return 'Claiming';
      case 'approve': return 'Approving';
      case 'distribute': return 'Distributing';
      default: return type;
    }
  };

  return (
    <>
      <div className="fixed bottom-4 right-4 z-40">
        <div className="bg-[#1E293B] border border-[#FFC700]/20 rounded-lg shadow-xl overflow-hidden">
          {/* Header */}
          <div
            className="flex items-center justify-between p-3 cursor-pointer hover:bg-[#FFC700]/5 transition-colors"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <div className="flex items-center space-x-2">
              {pendingTransactions.length > 0 ? (
                <ClockIcon className="w-5 h-5 text-yellow-400 animate-spin" />
              ) : (
                <CheckCircleIcon className="w-5 h-5 text-green-400" />
              )}
              <span className="text-white font-medium">
                {pendingTransactions.length > 0
                  ? `${pendingTransactions.length} Pending`
                  : 'Transactions'
                }
              </span>
              {pendingTransactions.length > 0 && (
                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" />
              )}
            </div>
            <div className="flex items-center space-x-1">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowManager(true);
                }}
                className="p-1 hover:bg-[#FFC700]/10 rounded text-[#FFC700] text-xs"
              >
                View All
              </button>
              {isExpanded ? (
                <ChevronDownIcon className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronUpIcon className="w-4 h-4 text-gray-400" />
              )}
            </div>
          </div>

          {/* Expanded Content */}
          {isExpanded && (
            <div className="border-t border-[#FFC700]/20 max-h-64 overflow-y-auto">
              {/* Pending Transactions */}
              {pendingTransactions.length > 0 && (
                <div className="p-3 border-b border-[#FFC700]/10">
                  <h4 className="text-xs font-medium text-[#FFC700] mb-2">PENDING</h4>
                  <div className="space-y-2">
                    {pendingTransactions.map((tx) => (
                      <div key={tx.id} className="flex items-center space-x-2 text-sm">
                        {getStatusIcon(tx.status)}
                        <span className="text-white flex-1">
                          {getTypeLabel(tx.type)}
                          {tx.metadata?.artistName && (
                            <span className="text-gray-400 ml-1">
                              - {tx.metadata.artistName}
                            </span>
                          )}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Recent Transactions */}
              {recentTransactions.length > 0 && (
                <div className="p-3">
                  <h4 className="text-xs font-medium text-gray-400 mb-2">RECENT</h4>
                  <div className="space-y-2">
                    {recentTransactions.map((tx) => (
                      <div key={tx.id} className="flex items-center space-x-2 text-sm">
                        {getStatusIcon(tx.status)}
                        <span className="text-white flex-1">
                          {getTypeLabel(tx.type)}
                          {tx.metadata?.artistName && (
                            <span className="text-gray-400 ml-1">
                              - {tx.metadata.artistName}
                            </span>
                          )}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {pendingTransactions.length === 0 && recentTransactions.length === 0 && (
                <div className="p-3 text-center text-gray-400 text-sm">
                  No recent transactions
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Transaction Manager Modal */}
      <TransactionManager
        isOpen={showManager}
        onClose={() => setShowManager(false)}
      />
    </>
  );
};

export default TransactionIndicator;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
      case 'confirming':
        return <ClockIcon className="w-4 h-4 text-yellow-400 animate-spin" />;
      case 'success':
        return <CheckCircleIcon className="w-4 h-4 text-green-400" />;
      case 'error':
        return <ExclamationCircleIcon className="w-4 h-4 text-red-400" />;
      default:
        return <ClockIcon className="w-4 h-4 text-gray-400" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'stake': return 'Staking';
      case 'unstake': return 'Unstaking';
      case 'claim': return 'Claiming';
      case 'approve': return 'Approving';
      case 'distribute': return 'Distributing';
      default: return type;
    }
  };

  return (
    <>
      <div className="fixed bottom-4 right-4 z-40">
        <div className="bg-[#1E293B] border border-[#FFC700]/20 rounded-lg shadow-xl overflow-hidden">
          {/* Header */}
          <div 
            className="flex items-center justify-between p-3 cursor-pointer hover:bg-[#FFC700]/5 transition-colors"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <div className="flex items-center space-x-2">
              {pendingTransactions.length > 0 ? (
                <ClockIcon className="w-5 h-5 text-yellow-400 animate-spin" />
              ) : (
                <CheckCircleIcon className="w-5 h-5 text-green-400" />
              )}
              <span className="text-white font-medium">
                {pendingTransactions.length > 0 
                  ? `${pendingTransactions.length} Pending`
                  : 'Transactions'
                }
              </span>
              {pendingTransactions.length > 0 && (
                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" />
              )}
            </div>
            <div className="flex items-center space-x-1">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowManager(true);
                }}
                className="p-1 hover:bg-[#FFC700]/10 rounded text-[#FFC700] text-xs"
              >
                View All
              </button>
              {isExpanded ? (
                <ChevronDownIcon className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronUpIcon className="w-4 h-4 text-gray-400" />
              )}
            </div>
          </div>

          {/* Expanded Content */}
          {isExpanded && (
            <div className="border-t border-[#FFC700]/20 max-h-64 overflow-y-auto">
              {/* Pending Transactions */}
              {pendingTransactions.length > 0 && (
                <div className="p-3 border-b border-[#FFC700]/10">
                  <h4 className="text-xs font-medium text-[#FFC700] mb-2">PENDING</h4>
                  <div className="space-y-2">
                    {pendingTransactions.map((tx) => (
                      <div key={tx.id} className="flex items-center space-x-2 text-sm">
                        {getStatusIcon(tx.status)}
                        <span className="text-white flex-1">
                          {getTypeLabel(tx.type)}
                          {tx.metadata?.artistName && (
                            <span className="text-gray-400 ml-1">
                              - {tx.metadata.artistName}
                            </span>
                          )}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Recent Transactions */}
              {recentTransactions.length > 0 && (
                <div className="p-3">
                  <h4 className="text-xs font-medium text-gray-400 mb-2">RECENT</h4>
                  <div className="space-y-2">
                    {recentTransactions.map((tx) => (
                      <div key={tx.id} className="flex items-center space-x-2 text-sm">
                        {getStatusIcon(tx.status)}
                        <span className="text-white flex-1">
                          {getTypeLabel(tx.type)}
                          {tx.metadata?.artistName && (
                            <span className="text-gray-400 ml-1">
                              - {tx.metadata.artistName}
                            </span>
                          )}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {pendingTransactions.length === 0 && recentTransactions.length === 0 && (
                <div className="p-3 text-center text-gray-400 text-sm">
                  No recent transactions
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Transaction Manager Modal */}
      <TransactionManager 
        isOpen={showManager} 
        onClose={() => setShowManager(false)} 
      />
    </>
  );
};

export default TransactionIndicator;
