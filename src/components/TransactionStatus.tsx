'use client';

import { useEffect, useState } from 'react';
import { useTransactions, Transaction } from '@/contexts/TransactionContext';
import { 
  CheckCircleIcon, 
  ExclamationCircleIcon,
  ClockIcon,
  XMarkIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';

interface TransactionStatusProps {
  transactionId?: string;
  onClose?: () => void;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

const TransactionStatus = ({ 
  transactionId, 
  onClose, 
  autoClose = true, 
  autoCloseDelay = 5000 
}: TransactionStatusProps) => {
  const { transactions, removeTransaction } = useTransactions();
  const [isVisible, setIsVisible] = useState(true);
  
  const transaction = transactionId 
    ? transactions.find(tx => tx.id === transactionId)
    : transactions.find(tx => tx.status === 'pending' || tx.status === 'confirming');

  useEffect(() => {
    if (transaction && autoClose && (transaction.status === 'success' || transaction.status === 'error')) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(() => {
          onClose?.();
          if (transactionId) {
            removeTransaction(transactionId);
          }
        }, 300);
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    }
  }, [transaction?.status, autoClose, autoCloseDelay, onClose, transactionId, removeTransaction]);

  if (!transaction || !isVisible) return null;

  const getStatusIcon = () => {
    switch (transaction.status) {
      case 'pending':
      case 'confirming':
        return <ClockIcon className="w-6 h-6 text-yellow-400 animate-spin" />;
      case 'success':
        return <CheckCircleIcon className="w-6 h-6 text-green-400" />;
      case 'error':
        return <ExclamationCircleIcon className="w-6 h-6 text-red-400" />;
      default:
        return <ClockIcon className="w-6 h-6 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (transaction.status) {
      case 'pending':
      case 'confirming':
        return 'border-yellow-400/30 bg-yellow-400/5';
      case 'success':
        return 'border-green-400/30 bg-green-400/5';
      case 'error':
        return 'border-red-400/30 bg-red-400/5';
      default:
        return 'border-gray-400/30 bg-gray-400/5';
    }
  };

  const getStatusMessage = () => {
    switch (transaction.status) {
      case 'pending':
        return 'Transaction submitted to blockchain...';
      case 'confirming':
        return 'Waiting for confirmation...';
      case 'success':
        return 'Transaction completed successfully!';
      case 'error':
        return transaction.error || 'Transaction failed';
      default:
        return 'Processing transaction...';
    }
  };

  const getTypeLabel = () => {
    switch (transaction.type) {
      case 'stake': return 'Staking NFT';
      case 'unstake': return 'Unstaking NFT';
      case 'claim': return 'Claiming Rewards';
      case 'approve': return 'Approving Contract';
      case 'distribute': return 'Distributing Royalties';
      default: return transaction.type;
    }
  };

  const getAvalancheExplorerUrl = (hash: string) => {
    return `https://snowtrace.io/tx/${hash}`;
  };

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose?.();
      if (transactionId) {
        removeTransaction(transactionId);
      }
    }, 300);
  };

  return (
    <div className={`fixed top-4 right-4 z-50 transition-all duration-300 ${
      isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
    }`}>
      <div className={`bg-[#1E293B] border rounded-lg shadow-xl p-4 min-w-80 max-w-md ${getStatusColor()}`}>
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <div>
              <h4 className="font-medium text-white">{getTypeLabel()}</h4>
              {transaction.metadata?.artistName && (
                <p className="text-sm text-gray-300">{transaction.metadata.artistName}</p>
              )}
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-1 hover:bg-gray-600/20 rounded text-gray-400 hover:text-white transition-colors"
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        </div>

        <div className="mb-3">
          <p className="text-sm text-gray-300">{getStatusMessage()}</p>
          
          {/* Progress bar for pending transactions */}
          {(transaction.status === 'pending' || transaction.status === 'confirming') && (
            <div className="mt-2">
              <div className="w-full bg-gray-700 rounded-full h-1">
                <div className="bg-yellow-400 h-1 rounded-full animate-pulse" style={{ width: '60%' }}></div>
              </div>
            </div>
          )}
        </div>

        {/* Transaction Details */}
        {transaction.metadata && (
          <div className="text-xs text-gray-400 space-y-1 mb-3">
            {transaction.metadata.tokenId && (
              <div>Token ID: #{transaction.metadata.tokenId}</div>
            )}
            {transaction.metadata.amount && (
              <div>Amount: {transaction.metadata.amount}</div>
            )}
          </div>
        )}

        {/* Transaction Hash */}
        {transaction.hash && (
          <div className="pt-2 border-t border-gray-600">
            <a
              href={getAvalancheExplorerUrl(transaction.hash)}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-1 text-xs text-[#FFC700] hover:text-[#FFC700]/80 transition-colors"
            >
              <span>View on Snowtrace</span>
              <ArrowTopRightOnSquareIcon className="w-3 h-3" />
            </a>
          </div>
        )}

        {/* Action Buttons */}
        {transaction.status === 'error' && (
          <div className="pt-2 border-t border-gray-600">
            <button
              onClick={handleClose}
              className="w-full px-3 py-1 bg-red-600/20 text-red-400 rounded text-sm hover:bg-red-600/30 transition-colors"
            >
              Dismiss
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TransactionStatus;
