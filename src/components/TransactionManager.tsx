'use client';

import { useState } from 'react';
import { useTransactions, Transaction } from '@/contexts/TransactionContext';
import { 
  XMarkIcon, 
  CheckCircleIcon, 
  ExclamationCircleIcon,
  ClockIcon,
  ArrowTopRightOnSquareIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

interface TransactionManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

const TransactionManager = ({ isOpen, onClose }: TransactionManagerProps) => {
  const { 
    transactions, 
    removeTransaction, 
    clearTransactions, 
    getPendingTransactions 
  } = useTransactions();
  
  const [filter, setFilter] = useState<'all' | 'pending' | 'success' | 'error'>('all');

  const filteredTransactions = transactions.filter(tx => {
    if (filter === 'all') return true;
    if (filter === 'pending') return tx.status === 'pending' || tx.status === 'confirming';
    return tx.status === filter;
  });

  const getStatusIcon = (status: Transaction['status']) => {
    switch (status) {
      case 'pending':
      case 'confirming':
        return <ClockIcon className="w-5 h-5 text-yellow-400 animate-spin" />;
      case 'success':
        return <CheckCircleIcon className="w-5 h-5 text-green-400" />;
      case 'error':
        return <ExclamationCircleIcon className="w-5 h-5 text-red-400" />;
      default:
        return <ClockIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: Transaction['status']) => {
    switch (status) {
      case 'pending':
      case 'confirming':
        return 'border-yellow-400/20 bg-yellow-400/5';
      case 'success':
        return 'border-green-400/20 bg-green-400/5';
      case 'error':
        return 'border-red-400/20 bg-red-400/5';
      default:
        return 'border-gray-400/20 bg-gray-400/5';
    }
  };

  const getTypeLabel = (type: Transaction['type']) => {
    switch (type) {
      case 'stake': return 'Stake NFT';
      case 'unstake': return 'Unstake NFT';
      case 'claim': return 'Claim Rewards';
      case 'approve': return 'Approve Contract';
      case 'distribute': return 'Distribute Royalties';
      default: return type;
    }
  };

  const getAvalancheExplorerUrl = (hash: string) => {
    return `https://snowtrace.io/tx/${hash}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />
      
      <div className="absolute right-0 top-0 h-full w-full max-w-md bg-[#1E293B] shadow-xl">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-[#FFC700]/20">
            <h2 className="text-xl font-royal-bold text-white">Transaction Manager</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-[#FFC700]/10 rounded-lg transition-colors"
            >
              <XMarkIcon className="w-5 h-5 text-gray-400" />
            </button>
          </div>

          {/* Filters */}
          <div className="p-4 border-b border-[#FFC700]/20">
            <div className="flex space-x-2">
              {(['all', 'pending', 'success', 'error'] as const).map((filterType) => (
                <button
                  key={filterType}
                  onClick={() => setFilter(filterType)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    filter === filterType
                      ? 'bg-[#FFC700] text-black'
                      : 'bg-[#FFC700]/10 text-[#FFC700] hover:bg-[#FFC700]/20'
                  }`}
                >
                  {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
                  {filterType === 'pending' && getPendingTransactions().length > 0 && (
                    <span className="ml-1 bg-yellow-400 text-black rounded-full px-1 text-xs">
                      {getPendingTransactions().length}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="p-4 border-b border-[#FFC700]/20">
            <button
              onClick={clearTransactions}
              disabled={transactions.length === 0}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-red-600/10 text-red-400 rounded-lg hover:bg-red-600/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <TrashIcon className="w-4 h-4" />
              <span>Clear All Transactions</span>
            </button>
          </div>

          {/* Transaction List */}
          <div className="flex-1 overflow-y-auto p-4 space-y-3">
            {filteredTransactions.length === 0 ? (
              <div className="text-center py-8">
                <ClockIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-400 font-elegant">
                  {filter === 'all' ? 'No transactions yet' : `No ${filter} transactions`}
                </p>
              </div>
            ) : (
              filteredTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className={`p-4 rounded-lg border ${getStatusColor(transaction.status)}`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(transaction.status)}
                      <span className="font-medium text-white">
                        {getTypeLabel(transaction.type)}
                      </span>
                    </div>
                    <button
                      onClick={() => removeTransaction(transaction.id)}
                      className="p-1 hover:bg-red-600/20 rounded text-gray-400 hover:text-red-400 transition-colors"
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </button>
                  </div>

                  {/* Transaction Details */}
                  {transaction.metadata && (
                    <div className="text-sm text-gray-300 mb-2">
                      {transaction.metadata.artistName && (
                        <div>Artist: {transaction.metadata.artistName}</div>
                      )}
                      {transaction.metadata.tokenId && (
                        <div>Token ID: #{transaction.metadata.tokenId}</div>
                      )}
                      {transaction.metadata.amount && (
                        <div>Amount: {transaction.metadata.amount}</div>
                      )}
                    </div>
                  )}

                  {/* Status and Time */}
                  <div className="flex items-center justify-between text-xs text-gray-400">
                    <span className="capitalize">{transaction.status}</span>
                    <span>{formatDistanceToNow(transaction.timestamp)} ago</span>
                  </div>

                  {/* Transaction Hash */}
                  {transaction.hash && (
                    <div className="mt-2 pt-2 border-t border-gray-600">
                      <a
                        href={getAvalancheExplorerUrl(transaction.hash)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-1 text-xs text-[#FFC700] hover:text-[#FFC700]/80 transition-colors"
                      >
                        <span>View on Snowtrace</span>
                        <ArrowTopRightOnSquareIcon className="w-3 h-3" />
                      </a>
                    </div>
                  )}

                  {/* Error Message */}
                  {transaction.error && (
                    <div className="mt-2 pt-2 border-t border-red-600/20">
                      <p className="text-xs text-red-400">{transaction.error}</p>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionManager;
