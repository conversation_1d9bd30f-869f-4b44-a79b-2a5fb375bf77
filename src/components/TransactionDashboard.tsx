'use client';

import { useState, useMemo } from 'react';
import { useTransactions } from '@/contexts/TransactionContext';
import { 
  ClockIcon, 
  CheckCircleIcon, 
  ExclamationCircleIcon,
  ChartBarIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow } from 'date-fns';

const TransactionDashboard = () => {
  const { transactions, clearTransactions, getPendingTransactions } = useTransactions();
  const [filter, setFilter] = useState<'all' | 'pending' | 'success' | 'error'>('all');

  const stats = useMemo(() => {
    const total = transactions.length;
    const pending = transactions.filter(tx => tx.status === 'pending' || tx.status === 'confirming').length;
    const success = transactions.filter(tx => tx.status === 'success').length;
    const error = transactions.filter(tx => tx.status === 'error').length;
    
    return { total, pending, success, error };
  }, [transactions]);

  const filteredTransactions = useMemo(() => {
    let filtered = transactions;
    
    if (filter !== 'all') {
      if (filter === 'pending') {
        filtered = transactions.filter(tx => tx.status === 'pending' || tx.status === 'confirming');
      } else {
        filtered = transactions.filter(tx => tx.status === filter);
      }
    }
    
    return filtered.sort((a, b) => b.timestamp - a.timestamp);
  }, [transactions, filter]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
      case 'confirming':
        return <ClockIcon className="w-5 h-5 text-yellow-400" />;
      case 'success':
        return <CheckCircleIcon className="w-5 h-5 text-green-400" />;
      case 'error':
        return <ExclamationCircleIcon className="w-5 h-5 text-red-400" />;
      default:
        return <ClockIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
      case 'confirming':
        return 'border-yellow-400/20 bg-yellow-400/5';
      case 'success':
        return 'border-green-400/20 bg-green-400/5';
      case 'error':
        return 'border-red-400/20 bg-red-400/5';
      default:
        return 'border-gray-400/20 bg-gray-400/5';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'stake': return 'Stake NFT';
      case 'unstake': return 'Unstake NFT';
      case 'claim': return 'Claim Rewards';
      case 'approve': return 'Approve Contract';
      case 'distribute': return 'Distribute Royalties';
      default: return type;
    }
  };

  if (transactions.length === 0) {
    return (
      <div className="bg-[#1E293B] rounded-lg p-8 text-center">
        <ChartBarIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-medium text-white mb-2">No Transactions Yet</h3>
        <p className="text-gray-400">Your transaction history will appear here once you start interacting with the platform.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-[#1E293B] rounded-lg p-4 border border-[#FFC700]/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Total</p>
              <p className="text-2xl font-bold text-white">{stats.total}</p>
            </div>
            <ChartBarIcon className="w-8 h-8 text-[#FFC700]" />
          </div>
        </div>
        
        <div className="bg-[#1E293B] rounded-lg p-4 border border-yellow-400/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Pending</p>
              <p className="text-2xl font-bold text-yellow-400">{stats.pending}</p>
            </div>
            <ClockIcon className="w-8 h-8 text-yellow-400" />
          </div>
        </div>
        
        <div className="bg-[#1E293B] rounded-lg p-4 border border-green-400/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Success</p>
              <p className="text-2xl font-bold text-green-400">{stats.success}</p>
            </div>
            <CheckCircleIcon className="w-8 h-8 text-green-400" />
          </div>
        </div>
        
        <div className="bg-[#1E293B] rounded-lg p-4 border border-red-400/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">Failed</p>
              <p className="text-2xl font-bold text-red-400">{stats.error}</p>
            </div>
            <ExclamationCircleIcon className="w-8 h-8 text-red-400" />
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex space-x-2">
          {(['all', 'pending', 'success', 'error'] as const).map((filterType) => (
            <button
              key={filterType}
              onClick={() => setFilter(filterType)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                filter === filterType
                  ? 'bg-[#FFC700] text-black'
                  : 'bg-[#FFC700]/10 text-[#FFC700] hover:bg-[#FFC700]/20'
              }`}
            >
              {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
            </button>
          ))}
        </div>
        
        <button
          onClick={clearTransactions}
          disabled={transactions.length === 0}
          className="flex items-center space-x-2 px-4 py-2 bg-red-600/10 text-red-400 rounded-lg hover:bg-red-600/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ArrowPathIcon className="w-4 h-4" />
          <span>Clear All</span>
        </button>
      </div>

      {/* Transaction List */}
      <div className="space-y-3">
        {filteredTransactions.length === 0 ? (
          <div className="bg-[#1E293B] rounded-lg p-8 text-center">
            <p className="text-gray-400">No {filter} transactions found.</p>
          </div>
        ) : (
          filteredTransactions.map((transaction) => (
            <div
              key={transaction.id}
              className={`bg-[#1E293B] rounded-lg p-4 border ${getStatusColor(transaction.status)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  {getStatusIcon(transaction.status)}
                  <div className="flex-1">
                    <h4 className="font-medium text-white">{getTypeLabel(transaction.type)}</h4>
                    {transaction.metadata?.artistName && (
                      <p className="text-sm text-gray-300">{transaction.metadata.artistName}</p>
                    )}
                    <p className="text-xs text-gray-400 mt-1">
                      {formatDistanceToNow(transaction.timestamp)} ago
                    </p>
                  </div>
                </div>
                
                <div className="text-right">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    transaction.status === 'success' ? 'bg-green-400/10 text-green-400' :
                    transaction.status === 'error' ? 'bg-red-400/10 text-red-400' :
                    'bg-yellow-400/10 text-yellow-400'
                  }`}>
                    {transaction.status}
                  </span>
                </div>
              </div>
              
              {transaction.error && (
                <div className="mt-3 pt-3 border-t border-gray-600">
                  <p className="text-sm text-red-400">{transaction.error}</p>
                </div>
              )}
              
              {transaction.hash && (
                <div className="mt-3 pt-3 border-t border-gray-600">
                  <a
                    href={`https://snowtrace.io/tx/${transaction.hash}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-[#FFC700] hover:text-[#FFC700]/80 transition-colors"
                  >
                    View on Snowtrace →
                  </a>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default TransactionDashboard;
