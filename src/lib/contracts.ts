import { 
  ROYALTY_NFT_CONTRACT_ADDRESS, 
  ROYALTY_NFT_CONTRACT_ABI 
} from '@/app/ContractABI/RoyaltyNFT';
import { 
  ROYALTY_STAKING_CONTRACT_ADDRESS, 
  ROYALTY_STAKING_CONTRACT_ABI 
} from '@/app/ContractABI/RoyaltyStaking';

// Contract configurations
export const CONTRACTS = {
  ROYALTY_NFT: {
    address: ROYALTY_NFT_CONTRACT_ADDRESS,
    abi: ROYALTY_NFT_CONTRACT_ABI,
  },
  ROYALTY_STAKING: {
    address: ROYALTY_STAKING_CONTRACT_ADDRESS,
    abi: ROYALTY_STAKING_CONTRACT_ABI,
  },
} as const;

// Type definitions for contract data
export interface Artist {
  rating: bigint;
  investmentTarget: bigint;
  totalRoyalties: bigint;
  artistAddress: string;
  name: string;
  description: string;
}

export interface Stake {
  tokenId: bigint;
  amount: bigint;
  stakeTimestamp: bigint;
  lastClaimTimestamp: bigint;
}

// Helper functions for data transformation
export function formatRating(rating: bigint): number {
  return Number(rating) / 100; // Convert from 250 to 2.5
}

export function formatTokenAmount(amount: bigint): number {
  return Number(amount);
}

export function formatTimestamp(timestamp: bigint): Date {
  return new Date(Number(timestamp) * 1000);
}

// Risk level calculation based on rating
export function calculateRiskLevel(rating: number): 'Low' | 'Medium' | 'High' {
  if (rating >= 4.0) return 'Low';
  if (rating >= 3.0) return 'Medium';
  return 'High';
}

// Genre mapping (since it's not stored on-chain, we'll use a mapping)
export const GENRE_MAPPING: Record<string, string> = {
  'Luna Rivers': 'Pop',
  'Echo Beats': 'Electronic',
  'Midnight Jazz': 'Jazz',
  'Rock Anthem': 'Rock',
  // Add more mappings as needed
};

export function getGenreForArtist(artistName: string): string {
  return GENRE_MAPPING[artistName] || 'Unknown';
}
