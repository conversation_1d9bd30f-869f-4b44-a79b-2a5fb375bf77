'use client';

import { useMemo } from 'react';
import { useAccount } from 'wagmi';
import { 
  useAllArtists, 
  useUserStakes, 
  useNFTBalance, 
  useTotalStakedPerToken,
  useRoyaltyPool 
} from './useContractReads';
import { 
  useStakeWithTransaction, 
  useUnstakeWithTransaction, 
  useClaimWithTransaction, 
  useApprovalWithTransaction 
} from './useContractActions';
import { 
  calculateRiskLevel, 
  getGenreForArtist, 
  formatRating 
} from '@/lib/contracts';

// Main hook that provides all platform functionality
export function useRoyaltyPlatform() {
  const { address } = useAccount();
  
  // Read operations
  const { data: artists, isLoading: isLoadingArtists, error: artistsError } = useAllArtists();
  const { data: userStakes, isLoading: isLoadingStakes, error: stakesError } = useUserStakes(address);
  
  // Write operations
  const stakeActions = useStakeWithTransaction();
  const unstakeActions = useUnstakeWithTransaction();
  const claimActions = useClaimWithTransaction();
  const approvalActions = useApprovalWithTransaction();

  // Transform artists data to match frontend interface
  const transformedArtists = useMemo(() => {
    if (!artists) return [];
    
    return artists.map(artist => ({
      id: artist.id,
      tokenId: artist.tokenId,
      name: artist.name,
      genre: getGenreForArtist(artist.name),
      rating: artist.rating,
      riskLevel: calculateRiskLevel(artist.rating),
      totalStaked: 0, // Will be populated by additional hook calls
      monthlyStreams: 0, // Mock data for now
      image: '/api/placeholder/300/300', // Mock data for now
      description: artist.description,
      nftPrice: 0.5, // Mock data for now
      availableShares: 1000, // Mock data for now
      totalShares: 5000, // Mock data for now
      investmentTarget: artist.investmentTarget,
      totalRoyalties: artist.totalRoyalties,
      artistAddress: artist.artistAddress,
    }));
  }, [artists]);

  // Transform user stakes data
  const transformedStakes = useMemo(() => {
    if (!userStakes || !artists) return [];
    
    return userStakes.map(stake => {
      const artist = artists.find(a => a.tokenId === stake.tokenId);
      return {
        id: stake.index,
        stakeIndex: stake.index,
        tokenId: stake.tokenId,
        artistName: artist?.name || `Token ${stake.tokenId}`,
        genre: artist ? getGenreForArtist(artist.name) : 'Unknown',
        stakeAmount: stake.amount,
        currentValue: stake.amount * 1.15, // Mock calculation
        monthlyEarnings: stake.amount * 0.08, // Mock calculation
        change: 15, // Mock data
        rating: artist?.rating || 0,
        shares: stake.amount,
        stakeTimestamp: stake.stakeTimestamp,
        lastClaimTimestamp: stake.lastClaimTimestamp,
      };
    });
  }, [userStakes, artists]);

  // Calculate portfolio summary
  const portfolioSummary = useMemo(() => {
    const totalStaked = transformedStakes.reduce((sum, stake) => sum + stake.stakeAmount, 0);
    const totalValue = transformedStakes.reduce((sum, stake) => sum + stake.currentValue, 0);
    const totalEarnings = transformedStakes.reduce((sum, stake) => sum + stake.monthlyEarnings, 0);
    const averageAPY = totalStaked > 0 ? (totalEarnings / totalStaked) * 12 * 100 : 0;

    return {
      totalStaked,
      totalValue,
      totalEarnings,
      averageAPY,
      totalGainLoss: totalValue - totalStaked,
      gainLossPercentage: totalStaked > 0 ? ((totalValue - totalStaked) / totalStaked) * 100 : 0,
    };
  }, [transformedStakes]);

  return {
    // Data
    artists: transformedArtists,
    userStakes: transformedStakes,
    portfolioSummary,
    userAddress: address,
    
    // Loading states
    isLoadingArtists,
    isLoadingStakes,
    isLoading: isLoadingArtists || isLoadingStakes,
    
    // Errors
    artistsError,
    stakesError,
    hasError: !!artistsError || !!stakesError,
    
    // Actions
    stake: stakeActions.stakeWithTransaction,
    unstake: unstakeActions.unstakeWithTransaction,
    claimRoyalties: claimActions.claimWithTransaction,
    approveStaking: approvalActions.approveWithTransaction,
    
    // Transaction states
    isStaking: stakeActions.isPending || stakeActions.isConfirming,
    isUnstaking: unstakeActions.isPending || unstakeActions.isConfirming,
    isClaiming: claimActions.isPending || claimActions.isConfirming,
    isApproving: approvalActions.isPending || approvalActions.isConfirming,
    
    // Transaction success states
    stakeSuccess: stakeActions.isSuccess,
    unstakeSuccess: unstakeActions.isSuccess,
    claimSuccess: claimActions.isSuccess,
    approvalSuccess: approvalActions.isSuccess,
  };
}

// Hook for individual artist data with staking info
export function useArtistWithStakingInfo(tokenId: number) {
  const { address } = useAccount();
  const { data: balance } = useNFTBalance(address, tokenId);
  const { data: totalStaked } = useTotalStakedPerToken(tokenId);
  const { data: royaltyPool } = useRoyaltyPool(tokenId);
  
  return {
    userBalance: balance ? Number(balance) : 0,
    totalStaked: totalStaked ? Number(totalStaked) : 0,
    royaltyPool: royaltyPool ? Number(royaltyPool) : 0,
  };
}

// Hook for staking pool data (for staking page)
export function useStakingPools() {
  const { artists } = useRoyaltyPlatform();
  const { address } = useAccount();
  
  // Transform artists into staking pool format
  const stakingPools = useMemo(() => {
    return artists.map(artist => ({
      id: artist.id,
      tokenId: artist.tokenId,
      artistName: artist.name,
      genre: artist.genre,
      apy: 24.5, // Mock APY calculation
      totalStaked: artist.totalStaked,
      userStaked: 0, // Will be calculated from user stakes
      pendingRewards: 0, // Will be calculated from contract
      lockPeriod: 30, // Mock data
      rating: artist.rating,
      riskLevel: artist.riskLevel,
      minStake: 0.1,
      maxStake: 10.0,
    }));
  }, [artists]);
  
  return {
    stakingPools,
    isLoading: false, // Will be updated based on actual loading states
  };
}
