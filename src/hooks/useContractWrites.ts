'use client';

import { useWriteContract, useWaitForTransactionReceipt } from 'wagmi';
import { CONTRACTS } from '@/lib/contracts';
import { parseEther } from 'viem';

// Hook for staking NFTs
export function useStakeNFT() {
  const { writeContract, data: hash, isPending, error } = useWriteContract();
  
  const { isLoading: isConfirming, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  const stake = async (tokenId: number, amount: number) => {
    try {
      await writeContract({
        ...CONTRACTS.ROYALTY_STAKING,
        functionName: 'stake',
        args: [BigInt(tokenId), BigInt(amount)],
      });
    } catch (err) {
      console.error('Staking failed:', err);
      throw err;
    }
  };

  return {
    stake,
    hash,
    isPending,
    isConfirming,
    isSuccess,
    error,
  };
}

// Hook for unstaking NFTs
export function useUnstakeNFT() {
  const { writeContract, data: hash, isPending, error } = useWriteContract();
  
  const { isLoading: isConfirming, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  const unstake = async (stakeIndex: number) => {
    try {
      await writeContract({
        ...CONTRACTS.ROYALTY_STAKING,
        functionName: 'unstake',
        args: [BigInt(stakeIndex)],
      });
    } catch (err) {
      console.error('Unstaking failed:', err);
      throw err;
    }
  };

  return {
    unstake,
    hash,
    isPending,
    isConfirming,
    isSuccess,
    error,
  };
}

// Hook for claiming royalties
export function useClaimRoyalties() {
  const { writeContract, data: hash, isPending, error } = useWriteContract();
  
  const { isLoading: isConfirming, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  const claimRoyalties = async (stakeIndex: number) => {
    try {
      await writeContract({
        ...CONTRACTS.ROYALTY_STAKING,
        functionName: 'claimRoyalties',
        args: [BigInt(stakeIndex)],
      });
    } catch (err) {
      console.error('Claiming royalties failed:', err);
      throw err;
    }
  };

  return {
    claimRoyalties,
    hash,
    isPending,
    isConfirming,
    isSuccess,
    error,
  };
}

// Hook for setting approval for staking contract
export function useSetApprovalForAll() {
  const { writeContract, data: hash, isPending, error } = useWriteContract();
  
  const { isLoading: isConfirming, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  const setApprovalForAll = async (approved: boolean) => {
    try {
      await writeContract({
        ...CONTRACTS.ROYALTY_NFT,
        functionName: 'setApprovalForAll',
        args: [CONTRACTS.ROYALTY_STAKING.address, approved],
      });
    } catch (err) {
      console.error('Setting approval failed:', err);
      throw err;
    }
  };

  return {
    setApprovalForAll,
    hash,
    isPending,
    isConfirming,
    isSuccess,
    error,
  };
}

// Hook to check if staking contract is approved
export function useIsApprovedForAll(userAddress: string | undefined) {
  const { writeContract, data: hash, isPending, error } = useWriteContract();
  
  const checkApproval = async () => {
    if (!userAddress) return false;
    
    try {
      const result = await writeContract({
        ...CONTRACTS.ROYALTY_NFT,
        functionName: 'isApprovedForAll',
        args: [userAddress as `0x${string}`, CONTRACTS.ROYALTY_STAKING.address],
      });
      return result;
    } catch (err) {
      console.error('Checking approval failed:', err);
      return false;
    }
  };

  return {
    checkApproval,
    hash,
    isPending,
    error,
  };
}

// Hook for distributing royalties (owner only)
export function useDistributeRoyalties() {
  const { writeContract, data: hash, isPending, error } = useWriteContract();
  
  const { isLoading: isConfirming, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  const distributeRoyalties = async (tokenId: number, amount: string) => {
    try {
      await writeContract({
        ...CONTRACTS.ROYALTY_STAKING,
        functionName: 'distributeRoyalties',
        args: [BigInt(tokenId)],
        value: parseEther(amount),
      });
    } catch (err) {
      console.error('Distributing royalties failed:', err);
      throw err;
    }
  };

  return {
    distributeRoyalties,
    hash,
    isPending,
    isConfirming,
    isSuccess,
    error,
  };
}
