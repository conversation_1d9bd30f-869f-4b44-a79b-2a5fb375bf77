'use client';

import { useReadContract, useReadContracts } from 'wagmi';
import { CONTRACTS, Artist, Stake, formatRating, formatTokenAmount } from '@/lib/contracts';

// Hook to get total number of artists/tokens
export function useTokenCounter() {
  return useReadContract({
    ...CONTRACTS.ROYALTY_NFT,
    functionName: 'tokenCounter',
  });
}

// Hook to get artist information by token ID
export function useArtistInfo(tokenId: number) {
  return useReadContract({
    ...CONTRACTS.ROYALTY_NFT,
    functionName: 'getArtistInfo',
    args: [BigInt(tokenId)],
    query: {
      enabled: tokenId > 0,
    },
  });
}

// Hook to get multiple artists data
export function useAllArtists() {
  const { data: tokenCounter, isLoading: isLoadingCounter } = useTokenCounter();
  
  const artistIds = tokenCounter ? Array.from({ length: Number(tokenCounter) - 1 }, (_, i) => i + 1) : [];
  
  const { data: artistsData, isLoading: isLoadingArtists, error } = useReadContracts({
    contracts: artistIds.map(id => ({
      ...CONTRACTS.ROYALTY_NFT,
      functionName: 'getArtistInfo',
      args: [BigInt(id)],
    })),
    query: {
      enabled: artistIds.length > 0,
    },
  });

  const artists = artistsData?.map((result, index) => {
    if (result.status === 'success' && result.result) {
      const artistData = result.result as Artist;
      return {
        id: artistIds[index],
        tokenId: artistIds[index],
        name: artistData.name,
        description: artistData.description,
        rating: formatRating(artistData.rating),
        investmentTarget: formatTokenAmount(artistData.investmentTarget),
        totalRoyalties: formatTokenAmount(artistData.totalRoyalties),
        artistAddress: artistData.artistAddress,
      };
    }
    return null;
  }).filter(Boolean) || [];

  return {
    data: artists,
    isLoading: isLoadingCounter || isLoadingArtists,
    error,
  };
}

// Hook to get user's NFT balance for a specific token
export function useNFTBalance(userAddress: string | undefined, tokenId: number) {
  return useReadContract({
    ...CONTRACTS.ROYALTY_NFT,
    functionName: 'balanceOf',
    args: userAddress ? [userAddress as `0x${string}`, BigInt(tokenId)] : undefined,
    query: {
      enabled: !!userAddress && tokenId > 0,
    },
  });
}

// Hook to get user's stake count
export function useUserStakeCount(userAddress: string | undefined) {
  return useReadContract({
    ...CONTRACTS.ROYALTY_STAKING,
    functionName: 'getUserStakeCount',
    args: userAddress ? [userAddress as `0x${string}`] : undefined,
    query: {
      enabled: !!userAddress,
    },
  });
}

// Hook to get user's specific stake
export function useUserStake(userAddress: string | undefined, stakeIndex: number) {
  return useReadContract({
    ...CONTRACTS.ROYALTY_STAKING,
    functionName: 'getUserStake',
    args: userAddress ? [userAddress as `0x${string}`, BigInt(stakeIndex)] : undefined,
    query: {
      enabled: !!userAddress && stakeIndex >= 0,
    },
  });
}

// Hook to get all user stakes
export function useUserStakes(userAddress: string | undefined) {
  const { data: stakeCount, isLoading: isLoadingCount } = useUserStakeCount(userAddress);
  
  const stakeIndices = stakeCount ? Array.from({ length: Number(stakeCount) }, (_, i) => i) : [];
  
  const { data: stakesData, isLoading: isLoadingStakes, error } = useReadContracts({
    contracts: stakeIndices.map(index => ({
      ...CONTRACTS.ROYALTY_STAKING,
      functionName: 'getUserStake',
      args: [userAddress as `0x${string}`, BigInt(index)],
    })),
    query: {
      enabled: !!userAddress && stakeIndices.length > 0,
    },
  });

  const stakes = stakesData?.map((result, index) => {
    if (result.status === 'success' && result.result) {
      const stakeData = result.result as Stake;
      return {
        index,
        tokenId: Number(stakeData.tokenId),
        amount: formatTokenAmount(stakeData.amount),
        stakeTimestamp: Number(stakeData.stakeTimestamp),
        lastClaimTimestamp: Number(stakeData.lastClaimTimestamp),
      };
    }
    return null;
  }).filter(Boolean) || [];

  return {
    data: stakes,
    isLoading: isLoadingCount || isLoadingStakes,
    error,
  };
}

// Hook to get total staked amount for a token
export function useTotalStakedPerToken(tokenId: number) {
  return useReadContract({
    ...CONTRACTS.ROYALTY_STAKING,
    functionName: 'totalStakedPerToken',
    args: [BigInt(tokenId)],
    query: {
      enabled: tokenId > 0,
    },
  });
}

// Hook to get royalty pool for a token
export function useRoyaltyPool(tokenId: number) {
  return useReadContract({
    ...CONTRACTS.ROYALTY_STAKING,
    functionName: 'royaltyPool',
    args: [BigInt(tokenId)],
    query: {
      enabled: tokenId > 0,
    },
  });
}

// Hook to check if user has approved staking contract
export function useIsApprovedForStaking(userAddress: string | undefined) {
  return useReadContract({
    ...CONTRACTS.ROYALTY_NFT,
    functionName: 'isApprovedForAll',
    args: userAddress ? [userAddress as `0x${string}`, CONTRACTS.ROYALTY_STAKING.address] : undefined,
    query: {
      enabled: !!userAddress,
    },
  });
}
