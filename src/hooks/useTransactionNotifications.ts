'use client';

import { useEffect, useRef } from 'react';
import { useTransactions } from '@/contexts/TransactionContext';
import toast from 'react-hot-toast';

export const useTransactionNotifications = () => {
  const { transactions } = useTransactions();
  const processedTransactions = useRef(new Set<string>());

  useEffect(() => {
    transactions.forEach((transaction) => {
      // Skip if we've already processed this transaction
      if (processedTransactions.current.has(transaction.id)) {
        return;
      }

      // Mark as processed
      processedTransactions.current.add(transaction.id);

      // Show notification based on status
      switch (transaction.status) {
        case 'pending':
          toast.loading(
            `${getTypeLabel(transaction.type)} transaction submitted...`,
            {
              id: transaction.id,
              duration: Infinity, // Keep until status changes
            }
          );
          break;

        case 'confirming':
          toast.loading(
            `Confirming ${getTypeLabel(transaction.type).toLowerCase()}...`,
            {
              id: transaction.id,
              duration: Infinity,
            }
          );
          break;

        case 'success':
          toast.dismiss(transaction.id);
          toast.success(
            `${getTypeLabel(transaction.type)} completed successfully!`,
            {
              duration: 4000,
            }
          );
          break;

        case 'error':
          toast.dismiss(transaction.id);
          toast.error(
            `${getTypeLabel(transaction.type)} failed: ${transaction.error || 'Unknown error'}`,
            {
              duration: 6000,
            }
          );
          break;
      }
    });

    // Clean up processed transactions that no longer exist
    const currentTransactionIds = new Set(transactions.map(tx => tx.id));
    processedTransactions.current.forEach((id) => {
      if (!currentTransactionIds.has(id)) {
        processedTransactions.current.delete(id);
        toast.dismiss(id);
      }
    });
  }, [transactions]);

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'stake': return 'Staking';
      case 'unstake': return 'Unstaking';
      case 'claim': return 'Claiming rewards';
      case 'approve': return 'Approval';
      case 'distribute': return 'Distribution';
      default: return type;
    }
  };
};

// Hook to manually trigger transaction notifications
export const useTransactionToasts = () => {
  const showPendingToast = (type: string, artistName?: string) => {
    const message = artistName 
      ? `${getTypeLabel(type)} for ${artistName}...`
      : `${getTypeLabel(type)}...`;
    
    return toast.loading(message, { duration: Infinity });
  };

  const showSuccessToast = (type: string, artistName?: string, hash?: string) => {
    const message = artistName 
      ? `${getTypeLabel(type)} for ${artistName} completed!`
      : `${getTypeLabel(type)} completed!`;
    
    toast.success(message, { duration: 4000 });
  };

  const showErrorToast = (type: string, error: string, artistName?: string) => {
    const message = artistName 
      ? `${getTypeLabel(type)} for ${artistName} failed: ${error}`
      : `${getTypeLabel(type)} failed: ${error}`;
    
    toast.error(message, { duration: 6000 });
  };

  const dismissToast = (toastId: string) => {
    toast.dismiss(toastId);
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'stake': return 'Staking';
      case 'unstake': return 'Unstaking';
      case 'claim': return 'Claiming rewards';
      case 'approve': return 'Approval';
      case 'distribute': return 'Distribution';
      default: return type;
    }
  };

  return {
    showPendingToast,
    showSuccessToast,
    showErrorToast,
    dismissToast,
  };
};
