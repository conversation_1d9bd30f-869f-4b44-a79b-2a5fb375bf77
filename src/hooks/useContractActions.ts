'use client';

import { useCallback, useEffect } from 'react';
import { useTransactionManager } from '@/contexts/TransactionContext';
import { 
  useStakeNFT, 
  useUnstakeNFT, 
  useClaimRoyalties, 
  useSetApprovalForAll 
} from './useContractWrites';
import { toast } from 'react-hot-toast';

// Enhanced staking hook with transaction management
export function useStakeWithTransaction() {
  const { createTransaction } = useTransactionManager();
  const { stake, hash, isPending, isConfirming, isSuccess, error } = useStakeNFT();

  const stakeWithTransaction = useCallback(async (tokenId: number, amount: number, artistName?: string) => {
    const transaction = createTransaction('stake', { tokenId, amount, artistName });
    
    try {
      await stake(tokenId, amount);
      toast.success(`Staking ${amount} NFTs for ${artistName || `Token ${tokenId}`}...`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Staking failed';
      transaction.setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    }
  }, [stake, createTransaction]);

  // Update transaction status based on contract state
  useEffect(() => {
    if (hash) {
      // Transaction hash received, update to confirming
      toast.loading('Confirming transaction...', { id: hash });
    }
  }, [hash]);

  useEffect(() => {
    if (isSuccess && hash) {
      toast.success('Staking successful!', { id: hash });
    }
  }, [isSuccess, hash]);

  useEffect(() => {
    if (error && hash) {
      toast.error('Staking failed!', { id: hash });
    }
  }, [error, hash]);

  return {
    stakeWithTransaction,
    hash,
    isPending,
    isConfirming,
    isSuccess,
    error,
  };
}

// Enhanced unstaking hook with transaction management
export function useUnstakeWithTransaction() {
  const { createTransaction } = useTransactionManager();
  const { unstake, hash, isPending, isConfirming, isSuccess, error } = useUnstakeNFT();

  const unstakeWithTransaction = useCallback(async (stakeIndex: number, artistName?: string) => {
    const transaction = createTransaction('unstake', { stakeIndex, artistName });
    
    try {
      await unstake(stakeIndex);
      toast.success(`Unstaking from ${artistName || `Stake ${stakeIndex}`}...`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unstaking failed';
      transaction.setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    }
  }, [unstake, createTransaction]);

  useEffect(() => {
    if (hash) {
      toast.loading('Confirming transaction...', { id: hash });
    }
  }, [hash]);

  useEffect(() => {
    if (isSuccess && hash) {
      toast.success('Unstaking successful!', { id: hash });
    }
  }, [isSuccess, hash]);

  useEffect(() => {
    if (error && hash) {
      toast.error('Unstaking failed!', { id: hash });
    }
  }, [error, hash]);

  return {
    unstakeWithTransaction,
    hash,
    isPending,
    isConfirming,
    isSuccess,
    error,
  };
}

// Enhanced claim royalties hook with transaction management
export function useClaimWithTransaction() {
  const { createTransaction } = useTransactionManager();
  const { claimRoyalties, hash, isPending, isConfirming, isSuccess, error } = useClaimRoyalties();

  const claimWithTransaction = useCallback(async (stakeIndex: number, artistName?: string) => {
    const transaction = createTransaction('claim', { stakeIndex, artistName });
    
    try {
      await claimRoyalties(stakeIndex);
      toast.success(`Claiming royalties from ${artistName || `Stake ${stakeIndex}`}...`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Claiming failed';
      transaction.setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    }
  }, [claimRoyalties, createTransaction]);

  useEffect(() => {
    if (hash) {
      toast.loading('Confirming transaction...', { id: hash });
    }
  }, [hash]);

  useEffect(() => {
    if (isSuccess && hash) {
      toast.success('Royalties claimed successfully!', { id: hash });
    }
  }, [isSuccess, hash]);

  useEffect(() => {
    if (error && hash) {
      toast.error('Claiming failed!', { id: hash });
    }
  }, [error, hash]);

  return {
    claimWithTransaction,
    hash,
    isPending,
    isConfirming,
    isSuccess,
    error,
  };
}

// Enhanced approval hook with transaction management
export function useApprovalWithTransaction() {
  const { createTransaction } = useTransactionManager();
  const { setApprovalForAll, hash, isPending, isConfirming, isSuccess, error } = useSetApprovalForAll();

  const approveWithTransaction = useCallback(async (approved: boolean = true) => {
    const transaction = createTransaction('approve', { approved });
    
    try {
      await setApprovalForAll(approved);
      toast.success(approved ? 'Approving staking contract...' : 'Revoking approval...');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Approval failed';
      transaction.setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    }
  }, [setApprovalForAll, createTransaction]);

  useEffect(() => {
    if (hash) {
      toast.loading('Confirming approval...', { id: hash });
    }
  }, [hash]);

  useEffect(() => {
    if (isSuccess && hash) {
      toast.success('Approval successful!', { id: hash });
    }
  }, [isSuccess, hash]);

  useEffect(() => {
    if (error && hash) {
      toast.error('Approval failed!', { id: hash });
    }
  }, [error, hash]);

  return {
    approveWithTransaction,
    hash,
    isPending,
    isConfirming,
    isSuccess,
    error,
  };
}
