import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Playfair_Display } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { ClientWalletWrapper } from "@/components/ClientWalletWrapper";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { TransactionProvider } from "@/contexts/TransactionContext";
import AnimatedBackground from "@/components/AnimatedBackground";
import { Toaster } from "react-hot-toast";
import TransactionIndicator from "@/components/TransactionIndicator";
import TransactionNotificationWrapper from "@/components/TransactionNotificationWrapper";

const roboto = Roboto({
  weight: ['300', '400', '500', '700'],
  subsets: ["latin"],
  variable: "--font-roboto",
});

const cinzel = Cinzel({
  weight: ['400', '500', '600', '700'],
  subsets: ["latin"],
  variable: "--font-royal",
});

const playfair = Playfair_Display({
  weight: ['400', '500', '600', '700'],
  subsets: ["latin"],
  variable: "--font-elegant",
});

export const metadata: Metadata = {
  title: "Royalty - Music Investment Platform",
  description: "Stake in artists' NFTs to earn music royalties. A decentralized music investment and royalty-sharing platform built on Avalanche blockchain.",
  keywords: "music, NFT, royalties, blockchain, investment, Avalanche, staking",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${roboto.variable} ${cinzel.variable} ${playfair.variable} font-sans antialiased min-h-screen`}>
        <ThemeProvider>
          <AnimatedBackground />
          <ClientWalletWrapper>
            <TransactionProvider>
              <div className="flex flex-col min-h-screen relative z-10">
                <Navigation />
                <main className="flex-1">
                  {children}
                </main>
                <Footer />
              </div>
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: '#1E293B',
                    color: '#FFC700',
                    border: '1px solid #FFC700',
                  },
                  success: {
                    iconTheme: {
                      primary: '#FFC700',
                      secondary: '#1E293B',
                    },
                  },
                  error: {
                    iconTheme: {
                      primary: '#EF4444',
                      secondary: '#1E293B',
                    },
                  },
                }}
              />
              <TransactionIndicator />
              <TransactionNotificationWrapper />
            </TransactionProvider>
          </ClientWalletWrapper>
        </ThemeProvider>
      </body>
    </html>
  );
}
